/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as TestRouteRouteImport } from './routes/test-route'
import { Route as EditorRouteImport } from './routes/editor'
import { Route as DecksRouteImport } from './routes/decks'
import { Route as DeckRouteImport } from './routes/deck'
import { Route as CustomsRouteImport } from './routes/customs'
import { Route as IndexRouteImport } from './routes/index'
import { Route as CustomIdEditRouteImport } from './routes/custom.$id.edit'

const TestRouteRoute = TestRouteRouteImport.update({
  id: '/test-route',
  path: '/test-route',
  getParentRoute: () => rootRouteImport,
} as any)
const EditorRoute = EditorRouteImport.update({
  id: '/editor',
  path: '/editor',
  getParentRoute: () => rootRouteImport,
} as any)
const DecksRoute = DecksRouteImport.update({
  id: '/decks',
  path: '/decks',
  getParentRoute: () => rootRouteImport,
} as any)
const DeckRoute = DeckRouteImport.update({
  id: '/deck',
  path: '/deck',
  getParentRoute: () => rootRouteImport,
} as any)
const CustomsRoute = CustomsRouteImport.update({
  id: '/customs',
  path: '/customs',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const CustomIdEditRoute = CustomIdEditRouteImport.update({
  id: '/custom/$id/edit',
  path: '/custom/$id/edit',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/customs': typeof CustomsRoute
  '/deck': typeof DeckRoute
  '/decks': typeof DecksRoute
  '/editor': typeof EditorRoute
  '/test-route': typeof TestRouteRoute
  '/custom/$id/edit': typeof CustomIdEditRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/customs': typeof CustomsRoute
  '/deck': typeof DeckRoute
  '/decks': typeof DecksRoute
  '/editor': typeof EditorRoute
  '/test-route': typeof TestRouteRoute
  '/custom/$id/edit': typeof CustomIdEditRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/customs': typeof CustomsRoute
  '/deck': typeof DeckRoute
  '/decks': typeof DecksRoute
  '/editor': typeof EditorRoute
  '/test-route': typeof TestRouteRoute
  '/custom/$id/edit': typeof CustomIdEditRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/customs'
    | '/deck'
    | '/decks'
    | '/editor'
    | '/test-route'
    | '/custom/$id/edit'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/customs'
    | '/deck'
    | '/decks'
    | '/editor'
    | '/test-route'
    | '/custom/$id/edit'
  id:
    | '__root__'
    | '/'
    | '/customs'
    | '/deck'
    | '/decks'
    | '/editor'
    | '/test-route'
    | '/custom/$id/edit'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  CustomsRoute: typeof CustomsRoute
  DeckRoute: typeof DeckRoute
  DecksRoute: typeof DecksRoute
  EditorRoute: typeof EditorRoute
  TestRouteRoute: typeof TestRouteRoute
  CustomIdEditRoute: typeof CustomIdEditRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/test-route': {
      id: '/test-route'
      path: '/test-route'
      fullPath: '/test-route'
      preLoaderRoute: typeof TestRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/editor': {
      id: '/editor'
      path: '/editor'
      fullPath: '/editor'
      preLoaderRoute: typeof EditorRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/decks': {
      id: '/decks'
      path: '/decks'
      fullPath: '/decks'
      preLoaderRoute: typeof DecksRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/deck': {
      id: '/deck'
      path: '/deck'
      fullPath: '/deck'
      preLoaderRoute: typeof DeckRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/customs': {
      id: '/customs'
      path: '/customs'
      fullPath: '/customs'
      preLoaderRoute: typeof CustomsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/custom/$id/edit': {
      id: '/custom/$id/edit'
      path: '/custom/$id/edit'
      fullPath: '/custom/$id/edit'
      preLoaderRoute: typeof CustomIdEditRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  CustomsRoute: CustomsRoute,
  DeckRoute: DeckRoute,
  DecksRoute: DecksRoute,
  EditorRoute: EditorRoute,
  TestRouteRoute: TestRouteRoute,
  CustomIdEditRoute: CustomIdEditRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
