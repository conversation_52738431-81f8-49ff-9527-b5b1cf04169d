import { useState, useEffect } from "react";
import { useNavigate, useParams } from "@tanstack/react-router";
import { ArrowLeft, Save } from "lucide-react";
import type { CustomCardType } from "../types/customCard";
import { FigmaLikeEditor } from "./FigmaLikeEditor";

export function CustomCardTypeDesigner() {
  const { id } = useParams({ from: "/custom/$id/edit" });
  const [cardType, setCardType] = useState<CustomCardType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // Debug logging
  console.log("CustomCardTypeDesigner loaded");
  console.log("ID from params:", id);
  console.log("Current URL:", window.location.href);

  // Load the card type from localStorage
  useEffect(() => {
    const stored = localStorage.getItem("customCardTypes");
    if (stored) {
      try {
        const cardTypes: CustomCardType[] = JSON.parse(stored);
        const foundCardType = cardTypes.find((ct) => ct.id === id);
        if (foundCardType) {
          setCardType(foundCardType);
        } else {
          // Card type not found, redirect back
          console.log("Card type not found, redirecting to /custom");
          window.location.href = "/custom";
          return;
        }
      } catch (error) {
        console.error("Failed to load custom card types:", error);
        window.location.href = "/custom";
        return;
      }
    } else {
      console.log("No stored card types, redirecting to /custom");
      window.location.href = "/custom";
      return;
    }
    setIsLoading(false);
  }, [id, navigate]);

  const saveCardType = (updatedCardType: CustomCardType) => {
    const stored = localStorage.getItem("customCardTypes");
    if (stored) {
      try {
        const cardTypes: CustomCardType[] = JSON.parse(stored);
        const updatedCardTypes = cardTypes.map((ct) =>
          ct.id === updatedCardType.id
            ? { ...updatedCardType, updatedAt: Date.now() }
            : ct
        );
        localStorage.setItem(
          "customCardTypes",
          JSON.stringify(updatedCardTypes)
        );
        setCardType({ ...updatedCardType, updatedAt: Date.now() });
      } catch (error) {
        console.error("Failed to save card type:", error);
      }
    }
  };

  const handleSave = () => {
    if (cardType) {
      saveCardType(cardType);
      // Show success message or toast
      alert("Card type saved successfully!");
    }
  };

  const handleBack = () => {
    navigate({ to: "/customs" });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading card type...</p>
        </div>
      </div>
    );
  }

  if (!cardType) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Card Type Not Found</h2>
          <p className="text-gray-600 mb-4">
            The requested card type could not be found.
          </p>
          <button
            onClick={handleBack}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Card Types
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="border-b bg-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center px-3 py-2 text-gray-700 hover:bg-gray-100 rounded"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </button>
            <div>
              <h1 className="text-2xl font-bold">{cardType.name}</h1>
              <p className="text-gray-600">
                {cardType.dimensions.width}×{cardType.dimensions.height}mm
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={handleSave}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              <Save className="w-4 h-4 mr-2" />
              Save
            </button>
          </div>
        </div>
      </div>

      {/* Main Content - Visual Design Only */}
      <div className="flex-1">
        <FigmaLikeEditor cardType={cardType} onChange={setCardType} />
      </div>
    </div>
  );
}
