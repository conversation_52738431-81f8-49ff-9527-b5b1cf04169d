import React, { useState, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Type,
  Image,
  Move,
  RotateCw,
  Eye,
  EyeOff,
  Lock,
  Unlock,
} from "lucide-react";
import type {
  CustomCardType,
  DesignComponent,
  TextComponentProperties,
  ImageComponentProperties,
} from "../types/customCard";

interface FigmaLikeEditorProps {
  cardType: CustomCardType;
  onChange: (cardType: CustomCardType) => void;
}

export function FigmaLikeEditor({ cardType, onChange }: FigmaLikeEditorProps) {
  const [selectedComponentId, setSelectedComponentId] = useState<string | null>(
    null
  );
  const [dragState, setDragState] = useState<{
    isDragging: boolean;
    dragType: "move" | "resize";
    startX: number;
    startY: number;
    startComponentX: number;
    startComponentY: number;
    startComponentWidth: number;
    startComponentHeight: number;
  } | null>(null);
  const canvasRef = useRef<HTMLDivElement>(null);

  // Convert mm to pixels for display (assuming 96 DPI for screen)
  const mmToPx = (mm: number) => (mm * 96) / 25.4;
  const pxToMm = (px: number) => (px * 25.4) / 96;

  const canvasWidth = mmToPx(cardType.dimensions.width);
  const canvasHeight = mmToPx(cardType.dimensions.height);

  const selectedComponent = cardType.components.find(
    (c) => c.id === selectedComponentId
  );

  const updateComponent = useCallback(
    (componentId: string, updates: Partial<DesignComponent>) => {
      const updatedComponents = cardType.components.map((component) =>
        component.id === componentId ? { ...component, ...updates } : component
      );
      onChange({ ...cardType, components: updatedComponents });
    },
    [cardType, onChange]
  );

  const addComponent = useCallback(
    (type: "text" | "image") => {
      const newComponent: DesignComponent = {
        id: `component_${Date.now()}`,
        type,
        name: type === "text" ? "Text Component" : "Image Component",
        x: 20,
        y: 20,
        width: type === "text" ? 120 : 100,
        height: type === "text" ? 30 : 100,
        rotation: 0,
        dataBinding: {
          type: "constant",
          value: type === "text" ? "Sample Text" : "",
        },
        opacity: 1,
        visible: true,
        locked: false,
      };

      if (type === "text") {
        newComponent.textProperties = {
          fontSize: 14,
          fontFamily: "Arial",
          fontWeight: "normal",
          color: "#000000",
          textAlign: "left",
          lineHeight: 1.2,
          letterSpacing: 0,
          textDecoration: "none",
          textTransform: "none",
        };
      } else {
        newComponent.imageProperties = {
          fit: "cover",
          borderRadius: 0,
          borderWidth: 0,
          borderColor: "#000000",
        };
      }

      onChange({
        ...cardType,
        components: [...cardType.components, newComponent],
      });
      setSelectedComponentId(newComponent.id);
    },
    [cardType, onChange]
  );

  const deleteComponent = useCallback(
    (componentId: string) => {
      const updatedComponents = cardType.components.filter(
        (c) => c.id !== componentId
      );
      onChange({ ...cardType, components: updatedComponents });
      if (selectedComponentId === componentId) {
        setSelectedComponentId(null);
      }
    },
    [cardType, onChange, selectedComponentId]
  );

  const handleMouseDown = useCallback(
    (e: React.MouseEvent, component: DesignComponent) => {
      if (component.locked) return;

      e.preventDefault();
      e.stopPropagation();

      setSelectedComponentId(component.id);

      const rect = canvasRef.current?.getBoundingClientRect();
      if (!rect) return;

      setDragState({
        isDragging: true,
        dragType: "move",
        startX: e.clientX,
        startY: e.clientY,
        startComponentX: component.x,
        startComponentY: component.y,
        startComponentWidth: component.width,
        startComponentHeight: component.height,
      });
    },
    []
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!dragState?.isDragging || !selectedComponentId) return;

      const deltaX = e.clientX - dragState.startX;
      const deltaY = e.clientY - dragState.startY;

      if (dragState.dragType === "move") {
        const newX = Math.max(
          0,
          Math.min(
            canvasWidth - dragState.startComponentWidth,
            dragState.startComponentX + deltaX
          )
        );
        const newY = Math.max(
          0,
          Math.min(
            canvasHeight - dragState.startComponentHeight,
            dragState.startComponentY + deltaY
          )
        );

        // Snap to grid (10px grid)
        const snappedX = Math.round(newX / 10) * 10;
        const snappedY = Math.round(newY / 10) * 10;

        updateComponent(selectedComponentId, { x: snappedX, y: snappedY });
      }
    },
    [dragState, selectedComponentId, canvasWidth, canvasHeight, updateComponent]
  );

  const handleMouseUp = useCallback(() => {
    setDragState(null);
  }, []);

  // Add global mouse event listeners
  React.useEffect(() => {
    if (dragState?.isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [dragState?.isDragging, handleMouseMove, handleMouseUp]);

  return (
    <div className="h-full flex">
      {/* Toolbar */}
      <div className="w-64 border-r bg-gray-50 p-4 space-y-4">
        <div>
          <h3 className="font-medium mb-3">Add Components</h3>
          <div className="space-y-2">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => addComponent("text")}
            >
              <Type className="w-4 h-4 mr-2" />
              Text
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => addComponent("image")}
            >
              <Image className="w-4 h-4 mr-2" />
              Image
            </Button>
          </div>
        </div>

        {/* Component List */}
        <div>
          <h3 className="font-medium mb-3">Components</h3>
          <div className="space-y-1">
            {cardType.components.map((component) => (
              <div
                key={component.id}
                className={`p-2 rounded cursor-pointer flex items-center justify-between ${
                  selectedComponentId === component.id
                    ? "bg-blue-100"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => setSelectedComponentId(component.id)}
              >
                <div className="flex items-center">
                  {component.type === "text" ? (
                    <Type className="w-4 h-4 mr-2" />
                  ) : (
                    <Image className="w-4 h-4 mr-2" />
                  )}
                  <span className="text-sm truncate">{component.name}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      updateComponent(component.id, {
                        visible: !component.visible,
                      });
                    }}
                    className="p-1 hover:bg-gray-200 rounded"
                  >
                    {component.visible ? (
                      <Eye className="w-3 h-3" />
                    ) : (
                      <EyeOff className="w-3 h-3" />
                    )}
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      updateComponent(component.id, {
                        locked: !component.locked,
                      });
                    }}
                    className="p-1 hover:bg-gray-200 rounded"
                  >
                    {component.locked ? (
                      <Lock className="w-3 h-3" />
                    ) : (
                      <Unlock className="w-3 h-3" />
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Canvas */}
      <div className="flex-1 p-4 overflow-auto">
        <div className="flex justify-center">
          <div
            ref={canvasRef}
            className="relative bg-white border-2 border-gray-300 shadow-lg"
            style={{
              width: `${canvasWidth}px`,
              height: `${canvasHeight}px`,
              backgroundImage:
                "radial-gradient(circle, #e5e7eb 1px, transparent 1px)",
              backgroundSize: "10px 10px",
            }}
            onClick={() => setSelectedComponentId(null)}
          >
            {cardType.components.map((component) => (
              <ComponentRenderer
                key={component.id}
                component={component}
                isSelected={selectedComponentId === component.id}
                onMouseDown={(e) => handleMouseDown(e, component)}
                onDelete={() => deleteComponent(component.id)}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Properties Panel */}
      {selectedComponent && (
        <div className="w-80 border-l bg-gray-50 p-4">
          <PropertiesPanel
            component={selectedComponent}
            onUpdate={(updates) =>
              updateComponent(selectedComponent.id, updates)
            }
          />
        </div>
      )}
    </div>
  );
}

interface ComponentRendererProps {
  component: DesignComponent;
  isSelected: boolean;
  onMouseDown: (e: React.MouseEvent) => void;
  onDelete: () => void;
}

function ComponentRenderer({
  component,
  isSelected,
  onMouseDown,
  onDelete,
}: ComponentRendererProps) {
  if (!component.visible) return null;

  const style: React.CSSProperties = {
    position: "absolute",
    left: `${component.x}px`,
    top: `${component.y}px`,
    width: `${component.width}px`,
    height: `${component.height}px`,
    transform: `rotate(${component.rotation}deg)`,
    opacity: component.opacity,
    cursor: component.locked ? "default" : "move",
    border: isSelected ? "2px solid #3b82f6" : "1px solid transparent",
    borderRadius: "2px",
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Delete" && isSelected) {
      onDelete();
    }
  };

  return (
    <div
      style={style}
      onMouseDown={onMouseDown}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      className="outline-none"
    >
      {component.type === "text" ? (
        <div
          style={{
            fontSize: `${component.textProperties?.fontSize || 14}px`,
            fontFamily: component.textProperties?.fontFamily || "Arial",
            fontWeight: component.textProperties?.fontWeight || "normal",
            color: component.textProperties?.color || "#000000",
            textAlign: component.textProperties?.textAlign || "left",
            lineHeight: component.textProperties?.lineHeight || 1.2,
            letterSpacing: `${component.textProperties?.letterSpacing || 0}px`,
            textDecoration: component.textProperties?.textDecoration || "none",
            textTransform: component.textProperties?.textTransform || "none",
            width: "100%",
            height: "100%",
            display: "flex",
            alignItems: "center",
            padding: "4px",
            overflow: "hidden",
          }}
        >
          {component.dataBinding.value}
        </div>
      ) : (
        <div
          style={{
            width: "100%",
            height: "100%",
            backgroundColor: "#f3f4f6",
            border: `${component.imageProperties?.borderWidth || 0}px solid ${component.imageProperties?.borderColor || "#000000"}`,
            borderRadius: `${component.imageProperties?.borderRadius || 0}px`,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            fontSize: "12px",
            color: "#6b7280",
          }}
        >
          {component.dataBinding.value ? (
            <img
              src={component.dataBinding.value}
              alt="Component"
              style={{
                width: "100%",
                height: "100%",
                objectFit: component.imageProperties?.fit || "cover",
                borderRadius: `${component.imageProperties?.borderRadius || 0}px`,
              }}
            />
          ) : (
            "📷 Image"
          )}
        </div>
      )}

      {isSelected && (
        <div className="absolute -top-6 -right-6 bg-blue-600 text-white text-xs px-2 py-1 rounded">
          {component.name}
        </div>
      )}
    </div>
  );
}

interface PropertiesPanelProps {
  component: DesignComponent;
  onUpdate: (updates: Partial<DesignComponent>) => void;
}

function PropertiesPanel({ component, onUpdate }: PropertiesPanelProps) {
  return (
    <div className="space-y-4">
      <div>
        <h3 className="font-medium mb-3">Properties</h3>

        {/* Basic Properties */}
        <div className="space-y-3">
          <div>
            <Label>Name</Label>
            <Input
              value={component.name}
              onChange={(e) => onUpdate({ name: e.target.value })}
              className="mt-1"
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label>X</Label>
              <Input
                type="number"
                value={component.x}
                onChange={(e) => onUpdate({ x: Number(e.target.value) })}
                className="mt-1"
              />
            </div>
            <div>
              <Label>Y</Label>
              <Input
                type="number"
                value={component.y}
                onChange={(e) => onUpdate({ y: Number(e.target.value) })}
                className="mt-1"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label>Width</Label>
              <Input
                type="number"
                value={component.width}
                onChange={(e) => onUpdate({ width: Number(e.target.value) })}
                className="mt-1"
              />
            </div>
            <div>
              <Label>Height</Label>
              <Input
                type="number"
                value={component.height}
                onChange={(e) => onUpdate({ height: Number(e.target.value) })}
                className="mt-1"
              />
            </div>
          </div>

          <div>
            <Label>Rotation (degrees)</Label>
            <Input
              type="number"
              value={component.rotation}
              onChange={(e) => onUpdate({ rotation: Number(e.target.value) })}
              className="mt-1"
            />
          </div>

          <div>
            <Label>Opacity</Label>
            <Input
              type="number"
              min="0"
              max="1"
              step="0.1"
              value={component.opacity}
              onChange={(e) => onUpdate({ opacity: Number(e.target.value) })}
              className="mt-1"
            />
          </div>
        </div>
      </div>

      {/* Data Binding */}
      <div>
        <h4 className="font-medium mb-2">Data Binding</h4>
        <div className="space-y-2">
          <div>
            <Label>Type</Label>
            <Select
              value={component.dataBinding.type}
              onValueChange={(value: "constant" | "field") =>
                onUpdate({
                  dataBinding: { ...component.dataBinding, type: value },
                })
              }
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="constant">Constant</SelectItem>
                <SelectItem value="field">Field Path</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label>
              {component.dataBinding.type === "constant"
                ? "Value"
                : "Field Path"}
            </Label>
            <Input
              value={component.dataBinding.value}
              onChange={(e) =>
                onUpdate({
                  dataBinding: {
                    ...component.dataBinding,
                    value: e.target.value,
                  },
                })
              }
              placeholder={
                component.dataBinding.type === "constant"
                  ? "Enter text or image URL"
                  : "e.g., name, stats.attack"
              }
              className="mt-1"
            />
          </div>
        </div>
      </div>

      {/* Type-specific Properties */}
      {component.type === "text" && component.textProperties && (
        <TextPropertiesPanel
          properties={component.textProperties}
          onUpdate={(updates) =>
            onUpdate({
              textProperties: { ...component.textProperties!, ...updates },
            })
          }
        />
      )}

      {component.type === "image" && component.imageProperties && (
        <ImagePropertiesPanel
          properties={component.imageProperties}
          onUpdate={(updates) =>
            onUpdate({
              imageProperties: { ...component.imageProperties!, ...updates },
            })
          }
        />
      )}
    </div>
  );
}

interface TextPropertiesPanelProps {
  properties: TextComponentProperties;
  onUpdate: (updates: Partial<TextComponentProperties>) => void;
}

function TextPropertiesPanel({
  properties,
  onUpdate,
}: TextPropertiesPanelProps) {
  return (
    <div>
      <h4 className="font-medium mb-2">Text Properties</h4>
      <div className="space-y-3">
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label>Font Size</Label>
            <Input
              type="number"
              value={properties.fontSize}
              onChange={(e) => onUpdate({ fontSize: Number(e.target.value) })}
              className="mt-1"
            />
          </div>
          <div>
            <Label>Font Weight</Label>
            <Select
              value={properties.fontWeight}
              onValueChange={(value: any) => onUpdate({ fontWeight: value })}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="normal">Normal</SelectItem>
                <SelectItem value="bold">Bold</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value="200">200</SelectItem>
                <SelectItem value="300">300</SelectItem>
                <SelectItem value="400">400</SelectItem>
                <SelectItem value="500">500</SelectItem>
                <SelectItem value="600">600</SelectItem>
                <SelectItem value="700">700</SelectItem>
                <SelectItem value="800">800</SelectItem>
                <SelectItem value="900">900</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label>Font Family</Label>
          <Select
            value={properties.fontFamily}
            onValueChange={(value) => onUpdate({ fontFamily: value })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Arial">Arial</SelectItem>
              <SelectItem value="Helvetica">Helvetica</SelectItem>
              <SelectItem value="Times New Roman">Times New Roman</SelectItem>
              <SelectItem value="Georgia">Georgia</SelectItem>
              <SelectItem value="Verdana">Verdana</SelectItem>
              <SelectItem value="Courier New">Courier New</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Color</Label>
          <Input
            type="color"
            value={properties.color}
            onChange={(e) => onUpdate({ color: e.target.value })}
            className="mt-1"
          />
        </div>

        <div>
          <Label>Text Align</Label>
          <Select
            value={properties.textAlign}
            onValueChange={(value: any) => onUpdate({ textAlign: value })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="left">Left</SelectItem>
              <SelectItem value="center">Center</SelectItem>
              <SelectItem value="right">Right</SelectItem>
              <SelectItem value="justify">Justify</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label>Line Height</Label>
            <Input
              type="number"
              step="0.1"
              value={properties.lineHeight}
              onChange={(e) => onUpdate({ lineHeight: Number(e.target.value) })}
              className="mt-1"
            />
          </div>
          <div>
            <Label>Letter Spacing</Label>
            <Input
              type="number"
              value={properties.letterSpacing}
              onChange={(e) =>
                onUpdate({ letterSpacing: Number(e.target.value) })
              }
              className="mt-1"
            />
          </div>
        </div>

        <div>
          <Label>Text Decoration</Label>
          <Select
            value={properties.textDecoration}
            onValueChange={(value: any) => onUpdate({ textDecoration: value })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">None</SelectItem>
              <SelectItem value="underline">Underline</SelectItem>
              <SelectItem value="line-through">Line Through</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Text Transform</Label>
          <Select
            value={properties.textTransform}
            onValueChange={(value: any) => onUpdate({ textTransform: value })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">None</SelectItem>
              <SelectItem value="uppercase">Uppercase</SelectItem>
              <SelectItem value="lowercase">Lowercase</SelectItem>
              <SelectItem value="capitalize">Capitalize</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}

interface ImagePropertiesPanelProps {
  properties: ImageComponentProperties;
  onUpdate: (updates: Partial<ImageComponentProperties>) => void;
}

function ImagePropertiesPanel({
  properties,
  onUpdate,
}: ImagePropertiesPanelProps) {
  return (
    <div>
      <h4 className="font-medium mb-2">Image Properties</h4>
      <div className="space-y-3">
        <div>
          <Label>Fit Mode</Label>
          <Select
            value={properties.fit}
            onValueChange={(value: any) => onUpdate({ fit: value })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cover">Cover</SelectItem>
              <SelectItem value="contain">Contain</SelectItem>
              <SelectItem value="fill">Fill</SelectItem>
              <SelectItem value="scale-down">Scale Down</SelectItem>
              <SelectItem value="none">None</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Border Radius</Label>
          <Input
            type="number"
            value={properties.borderRadius}
            onChange={(e) => onUpdate({ borderRadius: Number(e.target.value) })}
            className="mt-1"
          />
        </div>

        <div>
          <Label>Border Width</Label>
          <Input
            type="number"
            value={properties.borderWidth}
            onChange={(e) => onUpdate({ borderWidth: Number(e.target.value) })}
            className="mt-1"
          />
        </div>

        <div>
          <Label>Border Color</Label>
          <Input
            type="color"
            value={properties.borderColor}
            onChange={(e) => onUpdate({ borderColor: e.target.value })}
            className="mt-1"
          />
        </div>
      </div>
    </div>
  );
}
