import React, { useState, useRef, useCallback } from "react";
import {
  Type,
  Image,
  Move,
  RotateCw,
  Eye,
  EyeOff,
  Lock,
  Unlock,
} from "lucide-react";
import type {
  CustomCardType,
  DesignComponent,
  TextComponentProperties,
  ImageComponentProperties,
} from "../types/customCard";

interface FigmaLikeEditorProps {
  cardType: CustomCardType;
  onChange: (cardType: CustomCardType) => void;
}

export function FigmaLikeEditor({ cardType, onChange }: FigmaLikeEditorProps) {
  const [selectedComponentId, setSelectedComponentId] = useState<string | null>(
    null
  );
  const [dragState, setDragState] = useState<{
    isDragging: boolean;
    dragType: "move" | "resize";
    resizeHandle?: "nw" | "ne" | "sw" | "se" | "n" | "s" | "e" | "w";
    startX: number;
    startY: number;
    startComponentX: number;
    startComponentY: number;
    startComponentWidth: number;
    startComponentHeight: number;
  } | null>(null);
  const canvasRef = useRef<HTMLDivElement>(null);

  // Convert mm to pixels for display (assuming 96 DPI for screen)
  const mmToPx = (mm: number) => (mm * 96) / 25.4;
  const pxToMm = (px: number) => (px * 25.4) / 96;

  const canvasWidth = mmToPx(cardType.dimensions.width);
  const canvasHeight = mmToPx(cardType.dimensions.height);

  const selectedComponent = cardType.components.find(
    (c) => c.id === selectedComponentId
  );

  const updateComponent = useCallback(
    (componentId: string, updates: Partial<DesignComponent>) => {
      const updatedComponents = cardType.components.map((component) =>
        component.id === componentId ? { ...component, ...updates } : component
      );
      onChange({ ...cardType, components: updatedComponents });
    },
    [cardType, onChange]
  );

  const addComponent = useCallback(
    (type: "text" | "image") => {
      const newComponent: DesignComponent = {
        id: `component_${Date.now()}`,
        type,
        name: type === "text" ? "Text Component" : "Image Component",
        x: 20,
        y: 20,
        width: type === "text" ? 120 : 100,
        height: type === "text" ? 30 : 100,
        rotation: 0,
        dataBinding: {
          type: "constant",
          value: type === "text" ? "Sample Text" : "",
        },
        opacity: 1,
        visible: true,
        locked: false,
      };

      if (type === "text") {
        newComponent.textProperties = {
          fontSize: 14,
          fontFamily: "Arial",
          fontWeight: "normal",
          color: "#000000",
          textAlign: "left",
          lineHeight: 1.2,
          letterSpacing: 0,
          textDecoration: "none",
          textTransform: "none",
        };
      } else {
        newComponent.imageProperties = {
          fit: "cover",
          borderRadius: 0,
          borderWidth: 0,
          borderColor: "#000000",
        };
      }

      onChange({
        ...cardType,
        components: [...cardType.components, newComponent],
      });
      setSelectedComponentId(newComponent.id);
    },
    [cardType, onChange]
  );

  const deleteComponent = useCallback(
    (componentId: string) => {
      const updatedComponents = cardType.components.filter(
        (c) => c.id !== componentId
      );
      onChange({ ...cardType, components: updatedComponents });
      if (selectedComponentId === componentId) {
        setSelectedComponentId(null);
      }
    },
    [cardType, onChange, selectedComponentId]
  );

  const handleMouseDown = useCallback(
    (e: React.MouseEvent, component: DesignComponent) => {
      if (component.locked) return;

      e.preventDefault();
      e.stopPropagation();

      setSelectedComponentId(component.id);

      const rect = canvasRef.current?.getBoundingClientRect();
      if (!rect) return;

      setDragState({
        isDragging: true,
        dragType: "move",
        startX: e.clientX,
        startY: e.clientY,
        startComponentX: component.x,
        startComponentY: component.y,
        startComponentWidth: component.width,
        startComponentHeight: component.height,
      });
    },
    []
  );

  const handleResizeMouseDown = useCallback(
    (e: React.MouseEvent, component: DesignComponent, handle: string) => {
      if (component.locked) return;

      e.preventDefault();
      e.stopPropagation();

      setSelectedComponentId(component.id);

      setDragState({
        isDragging: true,
        dragType: "resize",
        resizeHandle: handle as any,
        startX: e.clientX,
        startY: e.clientY,
        startComponentX: component.x,
        startComponentY: component.y,
        startComponentWidth: component.width,
        startComponentHeight: component.height,
      });
    },
    []
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!dragState?.isDragging || !selectedComponentId) return;

      const deltaX = e.clientX - dragState.startX;
      const deltaY = e.clientY - dragState.startY;

      if (dragState.dragType === "move") {
        const newX = Math.max(
          0,
          Math.min(
            canvasWidth - dragState.startComponentWidth,
            dragState.startComponentX + deltaX
          )
        );
        const newY = Math.max(
          0,
          Math.min(
            canvasHeight - dragState.startComponentHeight,
            dragState.startComponentY + deltaY
          )
        );

        // Snap to grid (10px grid)
        const snappedX = Math.round(newX / 10) * 10;
        const snappedY = Math.round(newY / 10) * 10;

        updateComponent(selectedComponentId, { x: snappedX, y: snappedY });
      } else if (dragState.dragType === "resize" && dragState.resizeHandle) {
        const handle = dragState.resizeHandle;
        let newX = dragState.startComponentX;
        let newY = dragState.startComponentY;
        let newWidth = dragState.startComponentWidth;
        let newHeight = dragState.startComponentHeight;

        // Handle different resize directions
        if (handle.includes("e")) {
          newWidth = Math.max(20, dragState.startComponentWidth + deltaX);
        }
        if (handle.includes("w")) {
          const widthChange = -deltaX;
          newWidth = Math.max(20, dragState.startComponentWidth + widthChange);
          newX =
            dragState.startComponentX +
            dragState.startComponentWidth -
            newWidth;
        }
        if (handle.includes("s")) {
          newHeight = Math.max(20, dragState.startComponentHeight + deltaY);
        }
        if (handle.includes("n")) {
          const heightChange = -deltaY;
          newHeight = Math.max(
            20,
            dragState.startComponentHeight + heightChange
          );
          newY =
            dragState.startComponentY +
            dragState.startComponentHeight -
            newHeight;
        }

        // Snap to grid
        newX = Math.round(newX / 10) * 10;
        newY = Math.round(newY / 10) * 10;
        newWidth = Math.round(newWidth / 10) * 10;
        newHeight = Math.round(newHeight / 10) * 10;

        updateComponent(selectedComponentId, {
          x: newX,
          y: newY,
          width: newWidth,
          height: newHeight,
        });
      }
    },
    [dragState, selectedComponentId, canvasWidth, canvasHeight, updateComponent]
  );

  const handleMouseUp = useCallback(() => {
    setDragState(null);
  }, []);

  // Add global mouse event listeners
  React.useEffect(() => {
    if (dragState?.isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [dragState?.isDragging, handleMouseMove, handleMouseUp]);

  return (
    <div className="h-full flex">
      {/* Toolbar */}
      <div className="w-64 border-r bg-gray-50 p-4 space-y-4">
        <div>
          <h3 className="font-medium mb-3">Add Components</h3>
          <div className="space-y-2">
            <button
              className="w-full flex items-center justify-start px-3 py-2 border border-gray-300 rounded hover:bg-gray-100"
              onClick={() => addComponent("text")}
            >
              <Type className="w-4 h-4 mr-2" />
              Text
            </button>
            <button
              className="w-full flex items-center justify-start px-3 py-2 border border-gray-300 rounded hover:bg-gray-100"
              onClick={() => addComponent("image")}
            >
              <Image className="w-4 h-4 mr-2" />
              Image
            </button>
          </div>
        </div>

        {/* Component List */}
        <div>
          <h3 className="font-medium mb-3">Components</h3>
          <div className="space-y-1">
            {cardType.components.map((component) => (
              <div
                key={component.id}
                className={`p-2 rounded cursor-pointer flex items-center justify-between ${
                  selectedComponentId === component.id
                    ? "bg-blue-100"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => setSelectedComponentId(component.id)}
              >
                <div className="flex items-center">
                  {component.type === "text" ? (
                    <Type className="w-4 h-4 mr-2" />
                  ) : (
                    <Image className="w-4 h-4 mr-2" />
                  )}
                  <span className="text-sm truncate">{component.name}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      updateComponent(component.id, {
                        visible: !component.visible,
                      });
                    }}
                    className="p-1 hover:bg-gray-200 rounded"
                  >
                    {component.visible ? (
                      <Eye className="w-3 h-3" />
                    ) : (
                      <EyeOff className="w-3 h-3" />
                    )}
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      updateComponent(component.id, {
                        locked: !component.locked,
                      });
                    }}
                    className="p-1 hover:bg-gray-200 rounded"
                  >
                    {component.locked ? (
                      <Lock className="w-3 h-3" />
                    ) : (
                      <Unlock className="w-3 h-3" />
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Canvas */}
      <div className="flex-1 p-4 overflow-auto">
        <div className="flex justify-center">
          <div
            ref={canvasRef}
            className="relative bg-white border-2 border-gray-300 shadow-lg"
            style={{
              width: `${canvasWidth}px`,
              height: `${canvasHeight}px`,
              backgroundImage:
                "radial-gradient(circle, #e5e7eb 1px, transparent 1px)",
              backgroundSize: "10px 10px",
            }}
            onClick={() => setSelectedComponentId(null)}
          >
            {cardType.components.map((component) => (
              <ComponentRenderer
                key={component.id}
                component={component}
                isSelected={selectedComponentId === component.id}
                onMouseDown={(e) => handleMouseDown(e, component)}
                onResizeMouseDown={(e, handle) =>
                  handleResizeMouseDown(e, component, handle)
                }
                onDelete={() => deleteComponent(component.id)}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Properties Panel - Always visible */}
      <div className="w-80 border-l bg-gray-50 p-4 h-full overflow-hidden">
        <PropertiesPanel
          component={selectedComponent}
          onUpdate={(updates) =>
            selectedComponent && updateComponent(selectedComponent.id, updates)
          }
        />
      </div>
    </div>
  );
}

interface ComponentRendererProps {
  component: DesignComponent;
  isSelected: boolean;
  onMouseDown: (e: React.MouseEvent) => void;
  onResizeMouseDown: (e: React.MouseEvent, handle: string) => void;
  onDelete: () => void;
}

function ComponentRenderer({
  component,
  isSelected,
  onMouseDown,
  onResizeMouseDown,
  onDelete,
}: ComponentRendererProps) {
  if (!component.visible) return null;

  const style: React.CSSProperties = {
    position: "absolute",
    left: `${component.x}px`,
    top: `${component.y}px`,
    width: `${component.width}px`,
    height: `${component.height}px`,
    transform: `rotate(${component.rotation}deg)`,
    opacity: component.opacity,
    cursor: component.locked ? "default" : "move",
    border: isSelected ? "2px solid #3b82f6" : "1px solid transparent",
    borderRadius: "2px",
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Delete" && isSelected) {
      onDelete();
    }
  };

  const resizeHandles = [
    {
      position: "nw",
      cursor: "nw-resize",
      style: { top: "-4px", left: "-4px" },
    },
    {
      position: "ne",
      cursor: "ne-resize",
      style: { top: "-4px", right: "-4px" },
    },
    {
      position: "sw",
      cursor: "sw-resize",
      style: { bottom: "-4px", left: "-4px" },
    },
    {
      position: "se",
      cursor: "se-resize",
      style: { bottom: "-4px", right: "-4px" },
    },
    {
      position: "n",
      cursor: "n-resize",
      style: { top: "-4px", left: "50%", transform: "translateX(-50%)" },
    },
    {
      position: "s",
      cursor: "s-resize",
      style: { bottom: "-4px", left: "50%", transform: "translateX(-50%)" },
    },
    {
      position: "w",
      cursor: "w-resize",
      style: { left: "-4px", top: "50%", transform: "translateY(-50%)" },
    },
    {
      position: "e",
      cursor: "e-resize",
      style: { right: "-4px", top: "50%", transform: "translateY(-50%)" },
    },
  ];

  return (
    <div
      style={style}
      onMouseDown={onMouseDown}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      className="outline-none"
    >
      {component.type === "text" ? (
        <div
          style={{
            fontSize: `${component.textProperties?.fontSize || 14}px`,
            fontFamily: component.textProperties?.fontFamily || "Arial",
            fontWeight: component.textProperties?.fontWeight || "normal",
            color: component.textProperties?.color || "#000000",
            textAlign: component.textProperties?.textAlign || "left",
            lineHeight: component.textProperties?.lineHeight || 1.2,
            letterSpacing: `${component.textProperties?.letterSpacing || 0}px`,
            textDecoration: component.textProperties?.textDecoration || "none",
            textTransform: component.textProperties?.textTransform || "none",
            width: "100%",
            height: "100%",
            display: "flex",
            alignItems: "center",
            padding: "4px",
            overflow: "hidden",
          }}
        >
          {component.dataBinding.value}
        </div>
      ) : (
        <div
          style={{
            width: "100%",
            height: "100%",
            backgroundColor: "#f3f4f6",
            border: `${component.imageProperties?.borderWidth || 0}px solid ${component.imageProperties?.borderColor || "#000000"}`,
            borderRadius: `${component.imageProperties?.borderRadius || 0}px`,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            fontSize: "12px",
            color: "#6b7280",
          }}
        >
          {component.dataBinding.value ? (
            <img
              src={component.dataBinding.value}
              alt="Component"
              style={{
                width: "100%",
                height: "100%",
                objectFit: component.imageProperties?.fit || "cover",
                borderRadius: `${component.imageProperties?.borderRadius || 0}px`,
              }}
            />
          ) : (
            "📷 Image"
          )}
        </div>
      )}

      {isSelected && (
        <>
          <div className="absolute -top-6 -right-6 bg-blue-600 text-white text-xs px-2 py-1 rounded">
            {component.name}
          </div>

          {/* Resize Handles */}
          {!component.locked &&
            resizeHandles.map((handle) => (
              <div
                key={handle.position}
                className="absolute w-2 h-2 bg-blue-600 border border-white rounded-sm"
                style={{
                  ...handle.style,
                  cursor: handle.cursor,
                }}
                onMouseDown={(e) => {
                  e.stopPropagation();
                  onResizeMouseDown(e, handle.position);
                }}
              />
            ))}
        </>
      )}
    </div>
  );
}

interface PropertiesPanelProps {
  component: DesignComponent | null;
  onUpdate: (updates: Partial<DesignComponent>) => void;
}

function PropertiesPanel({ component, onUpdate }: PropertiesPanelProps) {
  if (!component) {
    return (
      <div className="space-y-4">
        <h3 className="font-medium mb-3">Properties</h3>
        <div className="text-center py-8 text-gray-500">
          <p>Select a component to edit its properties</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="font-medium mb-3">Properties</h3>

        {/* Basic Properties */}
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Name
            </label>
            <input
              type="text"
              value={component.name}
              onChange={(e) => onUpdate({ name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                X
              </label>
              <input
                type="number"
                value={component.x}
                onChange={(e) => onUpdate({ x: Number(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Y
              </label>
              <input
                type="number"
                value={component.y}
                onChange={(e) => onUpdate({ y: Number(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Width
              </label>
              <input
                type="number"
                value={component.width}
                onChange={(e) => onUpdate({ width: Number(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Height
              </label>
              <input
                type="number"
                value={component.height}
                onChange={(e) => onUpdate({ height: Number(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Rotation (degrees)
            </label>
            <input
              type="number"
              value={component.rotation}
              onChange={(e) => onUpdate({ rotation: Number(e.target.value) })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Opacity
            </label>
            <input
              type="number"
              min="0"
              max="1"
              step="0.1"
              value={component.opacity}
              onChange={(e) => onUpdate({ opacity: Number(e.target.value) })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Data Binding */}
      <div>
        <h4 className="font-medium mb-2">Data Binding</h4>
        <div className="space-y-2">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <select
              value={component.dataBinding.type}
              onChange={(e) =>
                onUpdate({
                  dataBinding: {
                    ...component.dataBinding,
                    type: e.target.value as "constant" | "field",
                  },
                })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="constant">Constant</option>
              <option value="field">Field Path</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {component.dataBinding.type === "constant"
                ? "Value"
                : "Field Path"}
            </label>
            <input
              type="text"
              value={component.dataBinding.value}
              onChange={(e) =>
                onUpdate({
                  dataBinding: {
                    ...component.dataBinding,
                    value: e.target.value,
                  },
                })
              }
              placeholder={
                component.dataBinding.type === "constant"
                  ? "Enter text or image URL"
                  : "e.g., name, stats.attack"
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Type-specific Properties */}
      {component.type === "text" && component.textProperties && (
        <TextPropertiesPanel
          properties={component.textProperties}
          onUpdate={(updates) =>
            onUpdate({
              textProperties: { ...component.textProperties!, ...updates },
            })
          }
        />
      )}

      {component.type === "image" && component.imageProperties && (
        <ImagePropertiesPanel
          properties={component.imageProperties}
          onUpdate={(updates) =>
            onUpdate({
              imageProperties: { ...component.imageProperties!, ...updates },
            })
          }
        />
      )}
    </div>
  );
}

interface TextPropertiesPanelProps {
  properties: TextComponentProperties;
  onUpdate: (updates: Partial<TextComponentProperties>) => void;
}

function TextPropertiesPanel({
  properties,
  onUpdate,
}: TextPropertiesPanelProps) {
  return (
    <div>
      <h4 className="font-medium mb-2">Text Properties</h4>
      <div className="space-y-3">
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Font Size
            </label>
            <input
              type="number"
              value={properties.fontSize}
              onChange={(e) => onUpdate({ fontSize: Number(e.target.value) })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Font Weight
            </label>
            <select
              value={properties.fontWeight}
              onChange={(e) => onUpdate({ fontWeight: e.target.value as any })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="normal">Normal</option>
              <option value="bold">Bold</option>
              <option value="100">100</option>
              <option value="200">200</option>
              <option value="300">300</option>
              <option value="400">400</option>
              <option value="500">500</option>
              <option value="600">600</option>
              <option value="700">700</option>
              <option value="800">800</option>
              <option value="900">900</option>
            </select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Font Family
          </label>
          <select
            value={properties.fontFamily}
            onChange={(e) => onUpdate({ fontFamily: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="Arial">Arial</option>
            <option value="Helvetica">Helvetica</option>
            <option value="Times New Roman">Times New Roman</option>
            <option value="Georgia">Georgia</option>
            <option value="Verdana">Verdana</option>
            <option value="Courier New">Courier New</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Color
          </label>
          <input
            type="color"
            value={properties.color}
            onChange={(e) => onUpdate({ color: e.target.value })}
            className="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Text Align
          </label>
          <select
            value={properties.textAlign}
            onChange={(e) => onUpdate({ textAlign: e.target.value as any })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="left">Left</option>
            <option value="center">Center</option>
            <option value="right">Right</option>
            <option value="justify">Justify</option>
          </select>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Line Height
            </label>
            <input
              type="number"
              step="0.1"
              value={properties.lineHeight}
              onChange={(e) => onUpdate({ lineHeight: Number(e.target.value) })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Letter Spacing
            </label>
            <input
              type="number"
              value={properties.letterSpacing}
              onChange={(e) =>
                onUpdate({ letterSpacing: Number(e.target.value) })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Text Decoration
          </label>
          <select
            value={properties.textDecoration}
            onChange={(e) =>
              onUpdate({ textDecoration: e.target.value as any })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="none">None</option>
            <option value="underline">Underline</option>
            <option value="line-through">Line Through</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Text Transform
          </label>
          <select
            value={properties.textTransform}
            onChange={(e) => onUpdate({ textTransform: e.target.value as any })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="none">None</option>
            <option value="uppercase">Uppercase</option>
            <option value="lowercase">Lowercase</option>
            <option value="capitalize">Capitalize</option>
          </select>
        </div>
      </div>
    </div>
  );
}

interface ImagePropertiesPanelProps {
  properties: ImageComponentProperties;
  onUpdate: (updates: Partial<ImageComponentProperties>) => void;
}

function ImagePropertiesPanel({
  properties,
  onUpdate,
}: ImagePropertiesPanelProps) {
  return (
    <div>
      <h4 className="font-medium mb-2">Image Properties</h4>
      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Fit Mode
          </label>
          <select
            value={properties.fit}
            onChange={(e) => onUpdate({ fit: e.target.value as any })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="cover">Cover</option>
            <option value="contain">Contain</option>
            <option value="fill">Fill</option>
            <option value="scale-down">Scale Down</option>
            <option value="none">None</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Border Radius
          </label>
          <input
            type="number"
            value={properties.borderRadius}
            onChange={(e) => onUpdate({ borderRadius: Number(e.target.value) })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Border Width
          </label>
          <input
            type="number"
            value={properties.borderWidth}
            onChange={(e) => onUpdate({ borderWidth: Number(e.target.value) })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Border Color
          </label>
          <input
            type="color"
            value={properties.borderColor}
            onChange={(e) => onUpdate({ borderColor: e.target.value })}
            className="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>
    </div>
  );
}
