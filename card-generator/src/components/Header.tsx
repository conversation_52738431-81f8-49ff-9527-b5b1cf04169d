import { Link } from "@tanstack/react-router";

export default function Header() {
  return (
    <header className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <div className="flex items-center gap-8">
          <Link
            to="/"
            className="text-xl font-bold text-gray-900 hover:text-blue-600"
          >
            CardForge
          </Link>
          <nav className="flex gap-6">
            <Link
              to="/"
              className="text-gray-600 hover:text-gray-900 [&.active]:text-blue-600 [&.active]:font-medium"
            >
              Home
            </Link>
            <Link
              to="/decks"
              className="text-gray-600 hover:text-gray-900 [&.active]:text-blue-600 [&.active]:font-medium"
            >
              Decks
            </Link>
            <Link
              to="/deck"
              className="text-gray-600 hover:text-gray-900 [&.active]:text-blue-600 [&.active]:font-medium"
            >
              Manage
            </Link>
            <Link
              to="/editor"
              className="text-gray-600 hover:text-gray-900 [&.active]:text-blue-600 [&.active]:font-medium"
            >
              Editor
            </Link>
            <Link
              to="/customs"
              className="text-gray-600 hover:text-gray-900 [&.active]:text-blue-600 [&.active]:font-medium"
            >
              Custom Types
            </Link>
          </nav>
        </div>
      </div>
    </header>
  );
}
